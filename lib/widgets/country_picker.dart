import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../models/country_model.dart';
import 'platform_icon.dart';

class CountryPicker extends StatelessWidget {
  final CountryModel selectedCountry;
  final Function(CountryModel) onCountrySelected;
  final double? width;
  final double? height;

  const CountryPicker({
    super.key,
    required this.selectedCountry,
    required this.onCountrySelected,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showCountryPicker(context),
      child: Container(
        width: width,
        height: height ?? MySize.size56,
        padding: EdgeInsets.symmetric(horizontal: MySize.size8),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: AppColors.borderColor),
          borderRadius: BorderRadius.circular(MySize.size10),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Flag
            Text(
              selectedCountry.flag,
              style: TextStyle(fontSize: MySize.size18),
            ),
            SizedBox(width: MySize.size4),
            // Dropdown arrow
            PlatformIcon(
              iconName: 'chevron_down',
              size: MySize.size16,
              color: AppColors.textSecondary,
            ),
          ],
        ),
      ),
    );
  }

  void _showCountryPicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _CountryPickerBottomSheet(
        selectedCountry: selectedCountry,
        onCountrySelected: onCountrySelected,
      ),
    );
  }
}

class _CountryPickerBottomSheet extends StatefulWidget {
  final CountryModel selectedCountry;
  final Function(CountryModel) onCountrySelected;

  const _CountryPickerBottomSheet({
    required this.selectedCountry,
    required this.onCountrySelected,
  });

  @override
  State<_CountryPickerBottomSheet> createState() => _CountryPickerBottomSheetState();
}

class _CountryPickerBottomSheetState extends State<_CountryPickerBottomSheet> {
  final TextEditingController _searchController = TextEditingController();
  List<CountryModel> _filteredCountries = [];
  List<CountryModel> _allCountries = [];

  @override
  void initState() {
    super.initState();
    _allCountries = CountryModel.getAllCountries();
    _filteredCountries = _allCountries;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterCountries(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredCountries = _allCountries;
      } else {
        _filteredCountries = _allCountries.where((country) {
          return country.name.toLowerCase().contains(query.toLowerCase()) ||
                 country.dialCode.contains(query);
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(MySize.size20),
          topRight: Radius.circular(MySize.size20),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: EdgeInsets.only(top: MySize.size8),
            width: MySize.size40,
            height: MySize.size4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(MySize.size2),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.all(MySize.size16),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Select Country',
                    style: TextStyle(
                      fontSize: MySize.size18,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: PlatformIcon(
                    iconName: 'close',
                    size: MySize.size24,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          // Search bar
          Padding(
            padding: EdgeInsets.symmetric(horizontal: MySize.size16),
            child: TextField(
              controller: _searchController,
              onChanged: _filterCountries,
              decoration: InputDecoration(
                hintText: 'Search country or dial code',
                prefixIcon: Padding(
                  padding: EdgeInsets.all(MySize.size12),
                  child: PlatformIcon(
                    iconName: 'search',
                    size: MySize.size20,
                    color: AppColors.textSecondary,
                  ),
                ),
                filled: true,
                fillColor: AppColors.backgroundColor,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(MySize.size8),
                  borderSide: BorderSide.none,
                ),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: MySize.size16,
                  vertical: MySize.size12,
                ),
              ),
            ),
          ),

          SizedBox(height: MySize.size16),

          // Countries list
          Expanded(
            child: ListView.builder(
              itemCount: _filteredCountries.length,
              itemBuilder: (context, index) {
                final country = _filteredCountries[index];
                final isSelected = country.code == widget.selectedCountry.code;

                return ListTile(
                  leading: Text(
                    country.flag,
                    style: TextStyle(fontSize: MySize.size24),
                  ),
                  title: Text(
                    country.name,
                    style: TextStyle(
                      fontSize: MySize.size16,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                      color: isSelected ? AppColors.primaryColor : AppColors.textPrimary,
                    ),
                  ),
                  trailing: Text(
                    country.dialCode,
                    style: TextStyle(
                      fontSize: MySize.size14,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  selected: isSelected,
                  selectedTileColor: AppColors.primaryColor.withValues(alpha: 0.1),
                  onTap: () {
                    widget.onCountrySelected(country);
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
