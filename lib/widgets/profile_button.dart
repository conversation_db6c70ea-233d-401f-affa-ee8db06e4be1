import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../services/firebase_service.dart';
import '../widgets/platform_icon.dart';

class ProfileButton extends StatelessWidget {
  final VoidCallback? onTap;
  final String? customName;
  
  const ProfileButton({
    super.key,
    this.onTap,
    this.customName,
  });

  @override
  Widget build(BuildContext context) {
    final FirebaseService firebaseService = Get.find<FirebaseService>();
    
    return Obx(() {
      // Get user name from Firebase user data or fallback to email or custom name
      String displayName = customName ?? 
                          firebaseService.userData?['displayName'] ?? 
                          firebaseService.user?.displayName ?? 
                          firebaseService.user?.email?.split('@').first ?? 
                          'User';
      
      return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(MySize.size8),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: MySize.size12,
            vertical: MySize.size8,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Profile Icon
              Container(
                width: MySize.size32,
                height: MySize.size32,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.primaryColor.withValues(alpha: 0.1),
                ),
                child: Icon(
                  Icons.person,
                  size: MySize.size18,
                  color: AppColors.primaryColor,
                ),
              ),
              SizedBox(width: MySize.size8),
              
              // User Name
              Text(
                displayName,
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              SizedBox(width: MySize.size8),
              
              // Dropdown Arrow
              PlatformIcon(
                iconName: 'chevron_down',
                size: MySize.size16,
                color: AppColors.textSecondary,
              ),
            ],
          ),
        ),
      );
    });
  }
}
