import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:packagingwala_web/common/custom_search_bar.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/platform_icon.dart';
import '../widgets/smart_svg_icon.dart';

import '../controllers/order_list_controller.dart';
import '../models/order_model.dart';
import '../common/common_sidebar.dart' as cs;
import '../common/global_search_bar.dart';

class OrderListScreen extends GetView<OrderListController> {
  const OrderListScreen({super.key});

  List<cs.NavItemData> get navItems => [
    cs.NavItemData(icon: 'home', label: 'Dashboard', index: 0),
    cs.NavItemData(icon: 'person', label: 'Customers List', index: 1),
    cs.NavItemData(icon: 'shopping_cart', label: 'Orders List', index: 2),
    cs.NavItemData(icon: 'person_add', label: 'Add Admin', index: 3),
  ];

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth <= 768;

          if (isMobile) {
            return Scaffold(
              backgroundColor: AppColors.backgroundColor,
              appBar: cs.AppMobileBar(),
              drawer: Obx(
                () => cs.AppMobileDrawer(
                  selectedIndex: controller.selectedNavIndex.value,
                  items: navItems,
                  onNavTap: controller.selectNavItem,
                ),
              ),
              body: _buildMainContent(),
            );
          }

          return Row(
            children: [
              Obx(
                () => cs.Sidebar(
                  selectedIndex: controller.selectedNavIndex.value,
                  items: navItems,
                  onNavTap: controller.selectNavItem,
                ),
              ),
              Expanded(child: _buildMainContent()),
            ],
          );
        },
      ),
    );
  }

  Widget _buildMainContent() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isTablet = constraints.maxWidth > 768;
        final isMobile = constraints.maxWidth <= 768;

        return Container(
          padding: EdgeInsets.all(isMobile ? MySize.size12 : MySize.size24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Top Search Bar
              if (isTablet)
                CustomSearchBar(
                  isTablet: true,
                  searchController: controller.searchController,
                  onChanged: controller.onSearchChanged,
                  hintText: "Search Orders",
                ),

              if (isTablet) SizedBox(height: MySize.size16),

              // Divider
              if (isTablet)
                Divider(color: AppColors.borderColor, thickness: 1, height: 1),

              if (isTablet) SizedBox(height: MySize.size24),

              // Header and Action Bar
              cs.AppHeaderAndActionRow(
                title: 'Order List',
                subtitle:
                    'Stay on top of every order. View statuses, check package details, and take quick actions anytime.',
                isMobile: isMobile,
                searchBar:
                    isMobile
                        ? CustomSearchBar(
                          isTablet: false,
                          searchController: controller.searchController,
                          onChanged: controller.onSearchChanged,
                          hintText: "Search Orders",
                        )
                        : null,
                actionButton:
                    isMobile
                        ? GestureDetector(
                          onTap: controller.onAddOrderTap,
                          child: Container(
                            width: double.infinity,
                            padding: EdgeInsets.symmetric(
                              vertical: MySize.size12,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.primaryColor,
                              borderRadius: BorderRadius.circular(MySize.size8),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                PlatformIcon(
                                  iconName: 'add',
                                  size: MySize.size16,
                                  color: AppColors.blackColor,
                                ),
                                SizedBox(width: MySize.size8),
                                Text(
                                  'Add Order',
                                  style: TextStyle(
                                    fontSize: MySize.size14,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.blackColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                        : GestureDetector(
                          onTap: controller.onAddOrderTap,
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: MySize.size16,
                              vertical: MySize.size12,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.primaryColor,
                              borderRadius: BorderRadius.circular(MySize.size8),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                PlatformIcon(
                                  iconName: 'add',
                                  size: MySize.size16,
                                  color: AppColors.blackColor,
                                ),
                                SizedBox(width: MySize.size8),
                                Text(
                                  'Add Order',
                                  style: TextStyle(
                                    fontSize: MySize.size14,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.blackColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
              ),

              SizedBox(height: isMobile ? MySize.size16 : MySize.size24),

              // Orders Table
              Expanded(child: _buildOrdersTable(isMobile: isMobile)),
            ],
          ),
        );
      },
    );
  }
  Widget _buildOrdersTable({bool isMobile = false}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Table Header
          if (!isMobile) _buildTableHeader(isMobile: isMobile),

          // Table Content
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              final orders = controller.paginatedOrders;
              if (orders.isEmpty) {
                return const Center(child: Text('No orders found'));
              }

              if (isMobile) {
                return ListView.builder(
                  padding: EdgeInsets.all(MySize.size8),
                  itemCount: orders.length,
                  itemBuilder: (context, index) {
                    return _buildMobileOrderCard(orders[index], index);
                  },
                );
              }

              return ListView.builder(
                itemCount: orders.length,
                itemBuilder: (context, index) {
                  return _buildTableRow(
                    orders[index],
                    index,
                    isMobile: isMobile,
                  );
                },
              );
            }),
          ),

          // Pagination
          _buildPagination(),
        ],
      ),
    );
  }

  Widget _buildTableHeader({bool isMobile = false}) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: MySize.size20,
        vertical: MySize.size16,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppColors.borderColor, width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              'ORDER ID',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'CUSTOMER',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'ORDER DATE',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'DELIVERY DATE',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'ITEMS',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              'AMOUNT',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'STATUS',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              'ACTION',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableRow(OrderModel order, int index, {bool isMobile = false}) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: MySize.size20,
        vertical: MySize.size16,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Order ID
          Expanded(
            flex: 2,
            child: Text(
              order.orderId,
              style: TextStyle(
                fontSize: MySize.size14,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          // Customer
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  order.customerName,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: MySize.size2),
                Text(
                  order.customerPhone,
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          // Order Date
          Expanded(
            flex: 2,
            child: Text(
              _formatDate(order.orderDate),
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          // Delivery Date
          Expanded(
            flex: 2,
            child: Text(
              _formatDate(order.deliveryDate),
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          // Items
          Expanded(
            flex: 2,
            child: Row(
              children: [
                SmartIcon(
                  assetPath: 'assets/icons/items_icon.svg',
                  width: MySize.size18,
                  height: MySize.size18,
                ),
                SizedBox(width: MySize.size8),
                Text(
                  '${order.itemCount} Items',
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),
          // Amount
          Expanded(
            flex: 1,
            child: Text(
              '₹ ${_formatAmount(order.amount)}',
              style: TextStyle(
                fontSize: MySize.size14,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          // Status
          Expanded(
            flex: 2,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: MySize.size12,
                vertical: MySize.size6,
              ),
              decoration: BoxDecoration(
                color: controller
                    .getStatusColor(order.status)
                    .withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(MySize.size16),
              ),
              child: Text(
                order.status,
                style: TextStyle(
                  fontSize: MySize.size12,
                  fontWeight: FontWeight.w500,
                  color: controller.getStatusColor(order.status),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          // Action
          Expanded(
            flex: 1,
            child: Center(
              child: PopupMenuButton<String>(
                icon: PlatformIcon(
                  iconName: 'more_vert',
                  size: MySize.size16,
                  color: AppColors.textSecondary,
                ),
                offset: Offset(0, MySize.size40),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(MySize.size12),
                ),
                elevation: 8,
                itemBuilder:
                    (context) => [
                      PopupMenuItem<String>(
                        value: 'view_details',
                        child: Row(
                          children: [
                            PlatformIcon(
                              iconName: 'visibility',
                              size: MySize.size20,
                              color: AppColors.textSecondary,
                            ),
                            SizedBox(width: MySize.size12),
                            Text(
                              'View Details',
                              style: TextStyle(
                                fontSize: MySize.size14,
                                fontWeight: FontWeight.w500,
                                color: AppColors.textPrimary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      PopupMenuItem<String>(
                        value: 'order_status',
                        child: Row(
                          children: [
                            PlatformIcon(
                              iconName: 'package',
                              size: MySize.size20,
                              color: AppColors.textSecondary,
                            ),
                            SizedBox(width: MySize.size12),
                            Text(
                              'Order Status',
                              style: TextStyle(
                                fontSize: MySize.size14,
                                fontWeight: FontWeight.w500,
                                color: AppColors.textPrimary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      PopupMenuItem<String>(
                        value: 'edit_order',
                        child: Row(
                          children: [
                            PlatformIcon(
                              iconName: 'edit',
                              size: MySize.size20,
                              color: AppColors.textSecondary,
                            ),
                            SizedBox(width: MySize.size12),
                            Text(
                              'Edit Order',
                              style: TextStyle(
                                fontSize: MySize.size14,
                                fontWeight: FontWeight.w500,
                                color: AppColors.textPrimary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      PopupMenuItem<String>(
                        value: 'delete_order',
                        child: Row(
                          children: [
                            PlatformIcon(
                              iconName: 'delete',
                              size: MySize.size20,
                              color: Colors.red,
                            ),
                            SizedBox(width: MySize.size12),
                            Text(
                              'Delete Order',
                              style: TextStyle(
                                fontSize: MySize.size14,
                                fontWeight: FontWeight.w500,
                                color: Colors.red,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                onSelected: (value) {
                  switch (value) {
                    case 'view_details':
                      controller.onViewOrderDetails(order);
                      break;
                    case 'order_status':
                      controller.onOrderStatus(order);
                      break;
                    case 'edit_order':
                      controller.onEditOrder(order);
                      break;
                    case 'delete_order':
                      controller.onDeleteOrder(order);
                      break;
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileOrderCard(OrderModel order, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: MySize.size12),
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size8),
        border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order ID and Status Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                order.orderId,
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.size8,
                  vertical: MySize.size4,
                ),
                decoration: BoxDecoration(
                  color: controller
                      .getStatusColor(order.status)
                      .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(MySize.size12),
                ),
                child: Text(
                  order.status,
                  style: TextStyle(
                    fontSize: MySize.size10,
                    fontWeight: FontWeight.w500,
                    color: controller.getStatusColor(order.status),
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size8),

          // Customer Info
          Text(
            order.customerName,
            style: TextStyle(
              fontSize: MySize.size13,
              fontWeight: FontWeight.w500,
              color: AppColors.textPrimary,
            ),
          ),
          Text(
            order.customerPhone,
            style: TextStyle(
              fontSize: MySize.size11,
              color: AppColors.textSecondary,
            ),
          ),

          SizedBox(height: MySize.size8),

          // Order Details
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Order Date',
                      style: TextStyle(
                        fontSize: MySize.size10,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      _formatDate(order.orderDate),
                      style: TextStyle(
                        fontSize: MySize.size12,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Delivery Date',
                      style: TextStyle(
                        fontSize: MySize.size10,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      _formatDate(order.deliveryDate),
                      style: TextStyle(
                        fontSize: MySize.size12,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size8),

          // Items and Amount
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${order.itemCount} Items',
                style: TextStyle(
                  fontSize: MySize.size12,
                  color: AppColors.textPrimary,
                ),
              ),
              Text(
                '₹ ${_formatAmount(order.amount)}',
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size12),

          // Action Button
          PopupMenuButton<String>(
            offset: Offset(0, MySize.size40),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(MySize.size12),
            ),
            elevation: 8,
            itemBuilder:
                (context) => [
                  PopupMenuItem<String>(
                    value: 'view_details',
                    child: Row(
                      children: [
                        PlatformIcon(
                          iconName: 'visibility',
                          size: MySize.size20,
                          color: AppColors.textSecondary,
                        ),
                        SizedBox(width: MySize.size12),
                        Text(
                          'View Details',
                          style: TextStyle(
                            fontSize: MySize.size14,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem<String>(
                    value: 'order_status',
                    child: Row(
                      children: [
                        PlatformIcon(
                          iconName: 'package',
                          size: MySize.size20,
                          color: AppColors.textSecondary,
                        ),
                        SizedBox(width: MySize.size12),
                        Text(
                          'Order Status',
                          style: TextStyle(
                            fontSize: MySize.size14,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem<String>(
                    value: 'edit_order',
                    child: Row(
                      children: [
                        PlatformIcon(
                          iconName: 'edit',
                          size: MySize.size20,
                          color: AppColors.textSecondary,
                        ),
                        SizedBox(width: MySize.size12),
                        Text(
                          'Edit Order',
                          style: TextStyle(
                            fontSize: MySize.size14,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem<String>(
                    value: 'delete_order',
                    child: Row(
                      children: [
                        PlatformIcon(
                          iconName: 'delete',
                          size: MySize.size20,
                          color: Colors.red,
                        ),
                        SizedBox(width: MySize.size12),
                        Text(
                          'Delete Order',
                          style: TextStyle(
                            fontSize: MySize.size14,
                            fontWeight: FontWeight.w500,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
            onSelected: (value) {
              switch (value) {
                case 'view_details':
                  controller.onViewOrderDetails(order);
                  break;
                case 'order_status':
                  controller.onOrderStatus(order);
                  break;
                case 'edit_order':
                  controller.onEditOrder(order);
                  break;
                case 'delete_order':
                  controller.onDeleteOrder(order);
                  break;
              }
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: MySize.size10),
              decoration: BoxDecoration(
                color: AppColors.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(MySize.size6),
                border: Border.all(color: AppColors.primaryColor),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  PlatformIcon(
                    iconName: 'more_vert',
                    size: MySize.size14,
                    color: AppColors.primaryColor,
                  ),
                  SizedBox(width: MySize.size6),
                  Text(
                    'Actions',
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPagination() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: MySize.size20,
        vertical: MySize.size16,
      ),
      child: Obx(() {
        final totalPages = controller.totalPages;
        final currentPage = controller.currentPage.value;

        return Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            // Previous Button
            GestureDetector(
              onTap:
                  currentPage > 1
                      ? () => controller.changePage(currentPage - 1)
                      : null,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.size12,
                  vertical: MySize.size8,
                ),

                child: PlatformIcon(
                  iconName: 'chevron_left',
                  size: MySize.size16,
                  color:
                      currentPage > 1
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                ),
              ),
            ),

            SizedBox(width: MySize.size4),

            // Page Numbers
            ...List.generate(totalPages > 5 ? 5 : totalPages, (index) {
              int pageNumber = index + 1;
              if (totalPages > 5 && currentPage > 3) {
                pageNumber = currentPage - 2 + index;
                if (pageNumber > totalPages) {
                  pageNumber = totalPages - 4 + index;
                }
              }

              return GestureDetector(
                onTap: () => controller.changePage(pageNumber),
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: MySize.size2),
                  padding: EdgeInsets.symmetric(
                    horizontal: MySize.size12,
                    vertical: MySize.size8,
                  ),
                  decoration: BoxDecoration(
                    color:
                        pageNumber == currentPage
                            ? AppColors.primaryColor
                            : Colors.white,
                    border: Border.all(color: AppColors.primaryColor),
                    borderRadius: BorderRadius.circular(MySize.size6),
                  ),
                  child: Text(
                    pageNumber.toString(),
                    style: TextStyle(
                      fontSize: MySize.size14,
                      fontWeight: FontWeight.w500,
                      color:
                          pageNumber == currentPage
                              ? AppColors.blackColor
                              : AppColors.textPrimary,
                    ),
                  ),
                ),
              );
            }),

            SizedBox(width: MySize.size4),

            // Next Button
            GestureDetector(
              onTap:
                  currentPage < totalPages
                      ? () => controller.changePage(currentPage + 1)
                      : null,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.size12,
                  vertical: MySize.size8,
                ),

                child: PlatformIcon(
                  iconName: 'chevron_right',
                  size: MySize.size16,
                  color:
                      currentPage < totalPages
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  String _formatDate(DateTime date) {
    const months = [
      'JAN',
      'FEB',
      'MAR',
      'APR',
      'MAY',
      'JUN',
      'JUL',
      'AUG',
      'SEP',
      'OCT',
      'NOV',
      'DEC',
    ];
    return '${months[date.month - 1]} ${date.day.toString().padLeft(2, '0')} ${date.year}';
  }

  String _formatAmount(double amount) {
    if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(0)}K';
    }
    return amount.toStringAsFixed(0);
  }
}
