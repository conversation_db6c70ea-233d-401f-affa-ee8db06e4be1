import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/platform_icon.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/profile_button.dart';
import '../controllers/add_admin_controller.dart';
import '../common/common_sidebar.dart' as cs;
import '../common/global_search_bar.dart';

class AddAdminScreen extends GetView<AddAdminController> {
  const AddAdminScreen({super.key});

  List<cs.NavItemData> get navItems => [
    cs.NavItemData(icon: 'home', label: 'Dashboard', index: 0),
    cs.NavItemData(icon: 'person', label: 'Customers List', index: 1),
    cs.NavItemData(icon: 'shopping_cart', label: 'Orders List', index: 2),
    cs.NavItemData(icon: 'person_add', label: 'Add Admin', index: 3),
  ];

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth <= 768;

          if (isMobile) {
            return Scaffold(
              backgroundColor: AppColors.backgroundColor,
              appBar: cs.AppMobileBar(),
              drawer: Obx(
                () => cs.AppMobileDrawer(
                  selectedIndex: controller.selectedNavIndex.value,
                  items: navItems,
                  onNavTap: controller.selectNavItem,
                ),
              ),
              body: _buildMainContent(),
            );
          }

          return Row(
            children: [
              Obx(
                () => cs.Sidebar(
                  selectedIndex: controller.selectedNavIndex.value,
                  items: navItems,
                  onNavTap: controller.selectNavItem,
                ),
              ),
              Expanded(child: _buildMainContent()),
            ],
          );
        },
      ),
    );
  }

  Widget _buildMainContent() {
    return Padding(
      padding: EdgeInsets.all(MySize.size24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          SizedBox(height: MySize.size32),
          Expanded(
            child: SingleChildScrollView(
              child: _buildAddAdminForm(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Add New Administrator',
                style: TextStyle(
                  fontSize: MySize.size28,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              SizedBox(height: MySize.size8),
              Text(
                'Add a new admin account with specific permissions and access levels.',
                style: TextStyle(
                  fontSize: MySize.size16,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: MySize.size16),
        // Search Bar
        GlobalSearchBar.header(
          hintText: 'Search Orders',
          width: MySize.size300,
        ),
        SizedBox(width: MySize.size16),
        // Profile Button
        ProfileButton(),
      ],
    );
  }

  Widget _buildAddAdminForm() {
    return Container(
      padding: EdgeInsets.all(MySize.size32),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Form(
        key: controller.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPersonalInformationSection(),
            SizedBox(height: MySize.size32),
            _buildAccountSettingsSection(),
            SizedBox(height: MySize.size40),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalInformationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            PlatformIcon(
              iconName: 'person',
              size: MySize.size20,
              color: AppColors.primaryColor,
            ),
            SizedBox(width: MySize.size8),
            Text(
              'Personal Information',
              style: TextStyle(
                fontSize: MySize.size18,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
        SizedBox(height: MySize.size8),
        Text(
          'Basic details about the new administrator.',
          style: TextStyle(
            fontSize: MySize.size14,
            color: AppColors.textSecondary,
          ),
        ),
        SizedBox(height: MySize.size24),
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: controller.fullNameController,
                hintText: 'Enter your full name here',
                labelText: 'Full Name',
                validator: controller.validateFullName,
                fillColor: Colors.white,
                borderColor: AppColors.borderColor,
              ),
            ),
            SizedBox(width: MySize.size16),
            Expanded(
              child: CustomTextField(
                controller: controller.mobileNumberController,
                hintText: '+91 XXXXXXXXXX',
                labelText: 'Mobile Number',
                keyboardType: TextInputType.phone,
                validator: controller.validateMobileNumber,
                fillColor: Colors.white,
                borderColor: AppColors.borderColor,
              ),
            ),
          ],
        ),
        SizedBox(height: MySize.size16),
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: controller.emailController,
                hintText: '<EMAIL>',
                labelText: 'Email Id',
                keyboardType: TextInputType.emailAddress,
                validator: controller.validateEmail,
                fillColor: Colors.white,
                borderColor: AppColors.borderColor,
              ),
            ),
            SizedBox(width: MySize.size16),
            Expanded(
              child: CustomTextField(
                controller: controller.addressController,
                hintText: 'Enter Full Address',
                labelText: 'Address',
                validator: controller.validateAddress,
                fillColor: Colors.white,
                borderColor: AppColors.borderColor,
                prefixIcon: Padding(
                  padding: EdgeInsets.all(MySize.size12),
                  child: PlatformIcon(
                    iconName: 'location',
                    size: MySize.size20,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAccountSettingsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            PlatformIcon(
              iconName: 'settings',
              size: MySize.size20,
              color: AppColors.primaryColor,
            ),
            SizedBox(width: MySize.size8),
            Text(
              'Account Settings',
              style: TextStyle(
                fontSize: MySize.size18,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
        SizedBox(height: MySize.size8),
        Text(
          'Configure login credentials and permissions for the new admin.',
          style: TextStyle(
            fontSize: MySize.size14,
            color: AppColors.textSecondary,
          ),
        ),
        SizedBox(height: MySize.size24),
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: controller.userNameController,
                hintText: 'Enter User Name',
                labelText: 'User Name',
                validator: controller.validateUserName,
                fillColor: Colors.white,
                borderColor: AppColors.borderColor,
              ),
            ),
            SizedBox(width: MySize.size16),
            Expanded(
              child: _buildAdminRoleDropdown(),
            ),
          ],
        ),
        SizedBox(height: MySize.size16),
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: controller.passwordController,
                hintText: 'Enter Temporary Password',
                labelText: 'Password',
                obscureText: true,
                validator: controller.validatePassword,
                fillColor: Colors.white,
                borderColor: AppColors.borderColor,
              ),
            ),
            SizedBox(width: MySize.size16),
            Expanded(
              child: CustomTextField(
                controller: controller.confirmPasswordController,
                hintText: 'Enter Confirm Password',
                labelText: 'Confirm Password',
                obscureText: true,
                validator: controller.validateConfirmPassword,
                fillColor: Colors.white,
                borderColor: AppColors.borderColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAdminRoleDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Admin Role',
          style: TextStyle(
            fontSize: MySize.size14,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: MySize.size8),
        Obx(() => Container(
          padding: EdgeInsets.symmetric(horizontal: MySize.size16),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: AppColors.borderColor),
            borderRadius: BorderRadius.circular(MySize.size12),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: controller.selectedAdminRole.value,
              isExpanded: true,
              icon: PlatformIcon(
                iconName: 'chevron_down',
                size: MySize.size20,
                color: AppColors.textSecondary,
              ),
              items: controller.adminRoles.map((String role) {
                return DropdownMenuItem<String>(
                  value: role,
                  child: Text(
                    role,
                    style: TextStyle(
                      fontSize: MySize.size16,
                      color: AppColors.textPrimary,
                    ),
                  ),
                );
              }).toList(),
              onChanged: (String? newValue) {
                if (newValue != null) {
                  controller.setAdminRole(newValue);
                }
              },
            ),
          ),
        )),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        SizedBox(
          width: MySize.size120,
          height: MySize.size48,
          child: OutlinedButton(
            onPressed: controller.handleCancel,
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: AppColors.borderColor),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(MySize.size8),
              ),
            ),
            child: Text(
              'Cancel',
              style: TextStyle(
                fontSize: MySize.size16,
                color: AppColors.textSecondary,
              ),
            ),
          ),
        ),
        SizedBox(width: MySize.size16),
        SizedBox(
          width: MySize.size180,
          height: MySize.size48,
          child: Obx(() => ElevatedButton.icon(
            onPressed: controller.isLoading.value ? null : controller.handleAddAdmin,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(MySize.size8),
              ),
            ),
            icon: controller.isLoading.value
                ? SizedBox(
                    width: MySize.size16,
                    height: MySize.size16,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : PlatformIcon(
                    iconName: 'person_add',
                    size: MySize.size18,
                    color: Colors.white,
                  ),
            label: Text(
              controller.isLoading.value ? 'Adding...' : 'Add Administrator',
              style: TextStyle(
                fontSize: MySize.size16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          )),
        ),
      ],
    );
  }
}
