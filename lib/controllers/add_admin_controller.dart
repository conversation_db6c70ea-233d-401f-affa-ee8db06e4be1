import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:developer' as dev;
import '../services/firebase_service.dart';
import '../routes/app_routes.dart';

class AddAdminController extends GetxController {
  // Firebase service
  final FirebaseService _firebaseService = Get.find<FirebaseService>();

  // Text controllers
  final fullNameController = TextEditingController();
  final mobileNumberController = TextEditingController();
  final emailController = TextEditingController();
  final addressController = TextEditingController();
  final userNameController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();

  // Observable variables
  final isLoading = false.obs;
  final selectedAdminRole = 'Super Administrator'.obs;
  final selectedNavIndex = 3.obs; // Add Admin is index 3

  // Form validation observables
  final fullNameText = ''.obs;
  final mobileNumberText = ''.obs;
  final emailText = ''.obs;
  final addressText = ''.obs;
  final userNameText = ''.obs;
  final passwordText = ''.obs;
  final confirmPasswordText = ''.obs;

  // Form key
  final formKey = GlobalKey<FormState>();

  // Admin role options
  final List<String> adminRoles = [
    'Super Administrator',
    'Administrator',
    'Manager',
    'Staff',
  ];

  @override
  void onInit() {
    super.onInit();
    // Listen to text changes
    fullNameController.addListener(() => fullNameText.value = fullNameController.text);
    mobileNumberController.addListener(() => mobileNumberText.value = mobileNumberController.text);
    emailController.addListener(() => emailText.value = emailController.text);
    addressController.addListener(() => addressText.value = addressController.text);
    userNameController.addListener(() => userNameText.value = userNameController.text);
    passwordController.addListener(() => passwordText.value = passwordController.text);
    confirmPasswordController.addListener(() => confirmPasswordText.value = confirmPasswordController.text);
  }

  @override
  void onClose() {
    fullNameController.dispose();
    mobileNumberController.dispose();
    emailController.dispose();
    addressController.dispose();
    userNameController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    super.onClose();
  }

  // Navigation methods
  void selectNavItem(int index) {
    selectedNavIndex.value = index;
    switch (index) {
      case 0:
        Get.offAllNamed(AppRoutes.dashboard);
        break;
      case 1:
        Get.offAllNamed(AppRoutes.customersList);
        break;
      case 2:
        Get.offAllNamed(AppRoutes.orderList);
        break;
      case 3:
        // Already on Add Admin screen
        break;
    }
  }

  // Form validation
  bool get isFormValid {
    return fullNameText.value.isNotEmpty &&
           mobileNumberText.value.isNotEmpty &&
           emailText.value.isNotEmpty &&
           addressText.value.isNotEmpty &&
           userNameText.value.isNotEmpty &&
           passwordText.value.isNotEmpty &&
           confirmPasswordText.value.isNotEmpty &&
           _firebaseService.isValidEmail(emailText.value) &&
           _firebaseService.isValidPassword(passwordText.value) &&
           passwordText.value == confirmPasswordText.value;
  }

  // Set admin role
  void setAdminRole(String role) {
    selectedAdminRole.value = role;
  }

  // Validate individual fields
  String? validateFullName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Full name is required';
    }
    return null;
  }

  String? validateMobileNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Mobile number is required';
    }
    if (value.length < 10) {
      return 'Please enter a valid mobile number';
    }
    return null;
  }

  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!_firebaseService.isValidEmail(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  String? validateAddress(String? value) {
    if (value == null || value.isEmpty) {
      return 'Address is required';
    }
    return null;
  }

  String? validateUserName(String? value) {
    if (value == null || value.isEmpty) {
      return 'User name is required';
    }
    if (value.length < 3) {
      return 'User name must be at least 3 characters';
    }
    return null;
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (!_firebaseService.isValidPassword(value)) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  String? validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Confirm password is required';
    }
    if (value != passwordText.value) {
      return 'Passwords do not match';
    }
    return null;
  }

  // Handle admin creation
  Future<void> handleAddAdmin() async {
    if (!formKey.currentState!.validate() || !isFormValid) {
      Get.snackbar(
        'Validation Error',
        'Please fill all fields correctly',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      isLoading.value = true;

      // Create admin in Firebase Authentication and Firestore
      final success = await _firebaseService.createAdmin(
        fullName: fullNameController.text.trim(),
        email: emailController.text.trim(),
        password: passwordController.text,
        mobileNumber: mobileNumberController.text.trim(),
        address: addressController.text.trim(),
        userName: userNameController.text.trim(),
        adminRole: selectedAdminRole.value,
      );

      if (success) {
        // Show success message
        Get.snackbar(
          'Success',
          'Admin created successfully!',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // Clear form
        _clearForm();
      }
    } catch (e) {
      dev.log('Error creating admin: $e');
      Get.snackbar(
        'Error',
        'Failed to create admin. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Clear form
  void _clearForm() {
    fullNameController.clear();
    mobileNumberController.clear();
    emailController.clear();
    addressController.clear();
    userNameController.clear();
    passwordController.clear();
    confirmPasswordController.clear();
    selectedAdminRole.value = 'Super Administrator';
  }

  // Cancel and go back
  void handleCancel() {
    Get.back();
  }
}
