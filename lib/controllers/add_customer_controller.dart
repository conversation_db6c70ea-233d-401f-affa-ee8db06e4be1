import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/add_customer_model.dart';
import '../models/country_model.dart';
import '../services/firebase_service.dart';

class AddCustomerController extends GetxController {
  // Services
  final FirebaseService _firebaseService = FirebaseService.instance;

  // Observable variables
  final RxInt selectedNavIndex = 1.obs; // Add Customer is accessed from Customers List (index 1)
  final RxBool isLoading = false.obs;
  final Rx<CountryModel> selectedCountry = CountryModel(
    name: 'India',
    code: 'IN',
    dialCode: '+91',
    flag: '🇮🇳',
  ).obs;

  // Form controllers
  final TextEditingController fullNameController = TextEditingController();
  final TextEditingController mobileNumberController = TextEditingController();
  final TextEditingController emailIdController = TextEditingController();
  final TextEditingController gstNumberController = TextEditingController();
  final TextEditingController businessNameController = TextEditingController();
  final TextEditingController businessAddressController = TextEditingController();
  final TextEditingController searchController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    _initializeMobileNumber();
  }

  @override
  void onClose() {
    fullNameController.dispose();
    mobileNumberController.dispose();
    emailIdController.dispose();
    gstNumberController.dispose();
    businessNameController.dispose();
    businessAddressController.dispose();
    searchController.dispose();
    super.onClose();
  }

  // Initialize mobile number with default country code
  void _initializeMobileNumber() {
    mobileNumberController.text = '${selectedCountry.value.dialCode} ';
  }

  // Handle navigation item selection
  void selectNavItem(int index) {
    selectedNavIndex.value = index;

    switch (index) {
      case 0:
        // Dashboard
        Get.offNamed('/dashboard');
        break;
      case 1:
        // Customers List
        Get.offNamed('/customers-list');
        break;
      case 2:
        // Orders List
        Get.offNamed('/order-list');
        break;
      case 3:
        // Add Admin
        Get.offNamed('/add-admin');
        break;
    }
  }

  // Handle country selection
  void selectCountry(CountryModel country) {
    selectedCountry.value = country;

    // Update mobile number with new country code if the field is empty or contains old country code
    final currentText = mobileNumberController.text.trim();
    if (currentText.isEmpty || currentText.startsWith('+')) {
      // If field is empty or starts with a country code, replace with new country code
      mobileNumberController.text = '${country.dialCode} ';
    }
  }

  // Handle form submission
  void onAddCustomerTap() async {
    if (_validateForm()) {
      isLoading.value = true;

      try {
        // Generate unique customer ID
        final customerId = 'CUST_${DateTime.now().millisecondsSinceEpoch}';

        final customerData = AddCustomerModel(
          customerId: customerId,
          fullName: fullNameController.text.trim(),
          mobileNumber: mobileNumberController.text.trim(),
          emailId: emailIdController.text.trim(),
          gstNumber: gstNumberController.text.trim(),
          businessName: businessNameController.text.trim(),
          businessAddress: businessAddressController.text.trim(),
        );

        // Save customer data to Firebase
        final success = await _firebaseService.saveCustomer(customerData);

        if (success) {
          Get.snackbar(
            'Success',
            'Customer ${customerData.fullName} added successfully!',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );

          // Clear form and navigate back to customers list
          _clearForm();
          Get.offNamed('/customers-list');
        }
      } catch (e) {
        Get.snackbar(
          'Error',
          'Failed to save customer. Please try again.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      } finally {
        isLoading.value = false;
      }
    }
  }

  // Form validation
  bool _validateForm() {
    if (fullNameController.text.isEmpty) {
      _showError('Please enter full name');
      return false;
    }
    if (mobileNumberController.text.isEmpty) {
      _showError('Please enter mobile number');
      return false;
    }
    if (emailIdController.text.isEmpty) {
      _showError('Please enter email ID');
      return false;
    }
    if (!GetUtils.isEmail(emailIdController.text)) {
      _showError('Please enter a valid email address');
      return false;
    }
    return true;
  }

  // Show error message
  void _showError(String message) {
    Get.snackbar(
      'Error',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }

  // Clear form fields
  void _clearForm() {
    fullNameController.clear();
    mobileNumberController.clear();
    emailIdController.clear();
    gstNumberController.clear();
    businessNameController.clear();
    businessAddressController.clear();

    // Reset mobile number with default country code
    _initializeMobileNumber();
  }

  // Handle search
  void onSearchChanged(String query) {
    // TODO: Implement search functionality if needed
  }
}
