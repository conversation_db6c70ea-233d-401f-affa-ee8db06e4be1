import 'package:flutter/material.dart';
import 'package:packagingwala_web/constants/app_colors.dart';
import 'package:packagingwala_web/constants/size.dart';
import 'package:packagingwala_web/widgets/custom_text_field.dart';
import 'package:packagingwala_web/widgets/platform_icon.dart';

class CustomSearchBar extends StatelessWidget {
  final TextEditingController searchController;
  final ValueChanged<String> onChanged;
  final String hintText;
  final bool isTablet;

  const CustomSearchBar({
    super.key,
    required this.isTablet,
    required this.searchController,
    required this.onChanged,
    this.hintText = 'Search...',
  });

  @override
  Widget build(BuildContext context) {
    return isTablet
        ? Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            SizedBox(
              width: 300,
              child: CustomTextField(
                controller: searchController,
                onChanged: onChanged,
                hintText: hintText,
                prefixIcon: Padding(
                  padding: EdgeInsets.all(MySize.size12),
                  child: PlatformIcon(
                    iconName: 'search',
                    size: MySize.size20,
                    color: AppColors.primaryColor,
                  ),
                ),
                fillColor: Colors.white,
                borderColor: AppColors.borderColor,
                borderRadius: MySize.size20,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: MySize.size16,
                  vertical: MySize.size10,
                ),
              ),
            ),
          ],
        )
        : CustomTextField(
          controller: searchController,
          hintText: 'Search customers...',
          onChanged: onChanged,
          prefixIcon: Padding(
            padding: EdgeInsets.all(MySize.size12),
            child: PlatformIcon(
              iconName: 'search',
              size: MySize.size18,
              color: AppColors.textSecondary,
            ),
          ),
          fillColor: Colors.white,
          borderColor: AppColors.borderColor,
          borderRadius: MySize.size8,
          contentPadding: EdgeInsets.symmetric(
            horizontal: MySize.size12,
            vertical: MySize.size10,
          ),
        );
  }
}
