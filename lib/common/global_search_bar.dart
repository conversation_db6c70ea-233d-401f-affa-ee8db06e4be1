import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/platform_icon.dart';
import '../widgets/custom_text_field.dart';

class GlobalSearchBar extends StatelessWidget {
  final TextEditingController? controller;
  final String hintText;
  final void Function(String)? onChanged;
  final double? width;
  final bool isMobile;
  final Color? fillColor;
  final Color? borderColor;
  final double? borderRadius;
  final EdgeInsetsGeometry? contentPadding;
  final double? iconSize;
  final Color? iconColor;
  final bool expanded;
  final MainAxisAlignment alignment;

  const GlobalSearchBar({
    super.key,
    this.controller,
    this.hintText = 'Search...',
    this.onChanged,
    this.width,
    this.isMobile = false,
    this.fillColor,
    this.borderColor,
    this.borderRadius,
    this.contentPadding,
    this.iconSize,
    this.iconColor,
    this.expanded = false,
    this.alignment = MainAxisAlignment.end,
  });

  /// Factory constructor for mobile search bar (used in AppHeaderAndActionRow)
  factory GlobalSearchBar.mobile({
    TextEditingController? controller,
    String hintText = 'Search...',
    void Function(String)? onChanged,
  }) {
    return GlobalSearchBar(
      controller: controller,
      hintText: hintText,
      onChanged: onChanged,
      isMobile: true,
      fillColor: Colors.white,
      borderColor: AppColors.borderColor,
      borderRadius: MySize.size8,
      contentPadding: EdgeInsets.symmetric(
        horizontal: MySize.size12,
        vertical: MySize.size10,
      ),
      iconSize: MySize.size18,
      iconColor: AppColors.textSecondary,
    );
  }

  /// Factory constructor for desktop top search bar (used in various screens)
  factory GlobalSearchBar.desktop({
    TextEditingController? controller,
    String hintText = 'Search Orders',
    void Function(String)? onChanged,
    double width = 300,
  }) {
    return GlobalSearchBar(
      controller: controller,
      hintText: hintText,
      onChanged: onChanged,
      width: width,
      isMobile: false,
      fillColor: Colors.white,
      borderColor: AppColors.borderColor,
      borderRadius: MySize.size20,
      contentPadding: EdgeInsets.symmetric(
        horizontal: MySize.size16,
        vertical: MySize.size10,
      ),
      iconSize: MySize.size20,
      iconColor: AppColors.primaryColor,
    );
  }

  /// Factory constructor for dashboard card detail search bar (expanded)
  factory GlobalSearchBar.expanded({
    TextEditingController? controller,
    String hintText = 'Search Orders..',
    void Function(String)? onChanged,
  }) {
    return GlobalSearchBar(
      controller: controller,
      hintText: hintText,
      onChanged: onChanged,
      expanded: true,
      fillColor: AppColors.backgroundColor,
      borderColor: AppColors.borderColor,
      borderRadius: MySize.size8,
      contentPadding: EdgeInsets.symmetric(
        horizontal: MySize.size16,
        vertical: MySize.size12,
      ),
      iconSize: MySize.size20,
      iconColor: AppColors.primaryColor,
    );
  }

  /// Factory constructor for header search bar (used in Add Admin screen)
  factory GlobalSearchBar.header({
    TextEditingController? controller,
    String hintText = 'Search Orders',
    void Function(String)? onChanged,
    double width = 300,
  }) {
    return GlobalSearchBar(
      controller: controller,
      hintText: hintText,
      onChanged: onChanged,
      width: width,
      fillColor: Colors.white,
      borderColor: AppColors.borderColor,
      borderRadius: MySize.size8,
      contentPadding: EdgeInsets.symmetric(
        horizontal: MySize.size16,
        vertical: MySize.size12,
      ),
      iconSize: MySize.size20,
      iconColor: AppColors.primaryColor,
    );
  }

  @override
  Widget build(BuildContext context) {
    final searchField = CustomTextField(
      controller: controller,
      hintText: hintText,
      onChanged: onChanged,
      prefixIcon: Padding(
        padding: EdgeInsets.all(MySize.size12),
        child: PlatformIcon(
          iconName: 'search',
          size: iconSize ?? MySize.size20,
          color: iconColor ?? AppColors.primaryColor,
        ),
      ),
      fillColor: fillColor ?? Colors.white,
      borderColor: borderColor ?? AppColors.borderColor,
      borderRadius: borderRadius ?? MySize.size12,
      contentPadding: contentPadding ?? EdgeInsets.symmetric(
        horizontal: MySize.size16,
        vertical: MySize.size12,
      ),
    );

    // If expanded, return the search field directly
    if (expanded) {
      return searchField;
    }

    // If width is specified, wrap in SizedBox
    if (width != null) {
      return SizedBox(
        width: width,
        child: searchField,
      );
    }

    // For mobile or when no specific width, return as is
    return searchField;
  }
}

/// Widget for top search bar row (used in multiple screens)
class TopSearchBarRow extends StatelessWidget {
  final TextEditingController? controller;
  final String hintText;
  final void Function(String)? onChanged;
  final double width;
  final MainAxisAlignment alignment;

  const TopSearchBarRow({
    super.key,
    this.controller,
    this.hintText = 'Search Orders',
    this.onChanged,
    this.width = 300,
    this.alignment = MainAxisAlignment.end,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: alignment,
      children: [
        GlobalSearchBar.desktop(
          controller: controller,
          hintText: hintText,
          onChanged: onChanged,
          width: width,
        ),
      ],
    );
  }
}
