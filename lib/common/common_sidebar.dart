import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/platform_icon.dart';
import '../widgets/smart_svg_icon.dart';
import '../services/firebase_service.dart';

class NavItemData {
  final String icon;
  final String label;
  final int index;
  final VoidCallback? onTap;
  NavItemData({
    required this.icon,
    required this.label,
    required this.index,
    this.onTap,
  });
}

class Sidebar extends StatelessWidget {
  final int selectedIndex;
  final List<NavItemData> items;
  final Function(int) onNavTap;
  final Widget? logo;
  const Sidebar({
    super.key,
    required this.selectedIndex,
    required this.items,
    required this.onNavTap,
    this.logo,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MySize.size200,
      color: AppColors.primaryColor,
      child: Column(
        children: [
          // Logo Section
          Container(
            padding: EdgeInsets.all(MySize.size20),
            child:
                logo ??
                SmartIcon(
                  assetPath: 'assets/icons/logo_icon.svg',
                  height: MySize.size70,
                  width: MySize.size129,
                  color: AppColors.blackColor,
                ),
          ),
          // Navigation Items
          Expanded(
            child: Column(
              children: [
                // Main navigation items
                ...items
                    .map(
                      (item) => SidebarNavItem(
                        icon: item.icon,
                        label: item.label,
                        isSelected: selectedIndex == item.index,
                        onTap: () => onNavTap(item.index),
                      ),
                    )
                    .toList(),

                // Spacer to push logout button to bottom
                const Spacer(),

                // Logout Button
                Container(
                  margin: EdgeInsets.symmetric(
                    horizontal: MySize.size16,
                    vertical: MySize.size8,
                  ),
                  child: SidebarNavItem(
                    icon: 'logout',
                    label: 'Log Out',
                    isSelected: false,
                    onTap: () async {
                      final FirebaseService firebaseService = Get.find<FirebaseService>();
                      await firebaseService.signOut();
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class SidebarNavItem extends StatelessWidget {
  final String icon;
  final String label;
  final bool isSelected;
  final VoidCallback? onTap;
  const SidebarNavItem({
    super.key,
    required this.icon,
    required this.label,
    required this.isSelected,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: MySize.size16,
        vertical: MySize.size4,
      ),
      decoration: BoxDecoration(
        color: isSelected ? Colors.white : Colors.transparent,
        borderRadius: BorderRadius.circular(MySize.size8),
      ),
      child: ListTile(
        leading: PlatformIcon(
          iconName: icon,
          size: MySize.size20,
          color: isSelected ? AppColors.blackColor : Colors.white,
        ),
        title: Text(
          label,
          style: TextStyle(
            fontSize: MySize.size14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected ? AppColors.blackColor : Colors.white,
          ),
        ),
        onTap: onTap,
      ),
    );
  }
}

class AppMobileBar extends StatelessWidget implements PreferredSizeWidget {
  final VoidCallback? onMenuTap;
  final Widget? logo;
  const AppMobileBar({super.key, this.onMenuTap, this.logo});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppColors.primaryColor,
      elevation: 0,
      leading: Builder(
        builder:
            (context) => IconButton(
              icon: PlatformIcon(
                iconName: 'menu',
                size: MySize.size24,
                color: AppColors.blackColor,
              ),
              onPressed: onMenuTap ?? () => Scaffold.of(context).openDrawer(),
            ),
      ),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          logo ??
              SmartIcon(
                assetPath: 'assets/icons/logo_icon.svg',
                height: MySize.size80,
                width: MySize.size84,
                color: AppColors.blackColor,
              ),
        ],
      ),
      centerTitle: false,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class AppMobileDrawer extends StatelessWidget {
  final int selectedIndex;
  final List<NavItemData> items;
  final Function(int) onNavTap;
  final Widget? logo;
  const AppMobileDrawer({
    super.key,
    required this.selectedIndex,
    required this.items,
    required this.onNavTap,
    this.logo,
  });

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: AppColors.primaryColor,
      child: SafeArea(
        child: Column(
          children: [
            // Logo Section
            Container(
              padding: EdgeInsets.all(MySize.size20),
              child:
                  logo ??
                  SmartIcon(
                    assetPath: 'assets/icons/logo_icon.svg',
                    height: MySize.size100,
                    width: MySize.size100,
                    color: AppColors.blackColor,
                  ),
            ),
            // Divider
            Divider(
              color: AppColors.blackColor.withOpacity(0.2),
              thickness: 1,
              indent: MySize.size16,
              endIndent: MySize.size16,
            ),
            // Navigation Items
            Expanded(
              child: Column(
                children: [
                  // Main navigation items
                  ...items
                      .map(
                        (item) => AppMobileDrawerNavItem(
                          icon: item.icon,
                          label: item.label,
                          isSelected: selectedIndex == item.index,
                          onTap: () {
                            Navigator.of(context).pop();
                            onNavTap(item.index);
                          },
                        ),
                      ),

                  // Spacer to push logout button to bottom
                  const Spacer(),

                  // Logout Button
                  Container(
                    margin: EdgeInsets.symmetric(
                      horizontal: MySize.size16,
                      vertical: MySize.size8,
                    ),
                    child: AppMobileDrawerNavItem(
                      icon: 'logout',
                      label: 'Log Out',
                      isSelected: false,
                      onTap: () async {
                        Navigator.of(context).pop(); // Close drawer first
                        final FirebaseService firebaseService = Get.find<FirebaseService>();
                        await firebaseService.signOut();
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AppMobileDrawerNavItem extends StatelessWidget {
  final String icon;
  final String label;
  final bool isSelected;
  final VoidCallback onTap;
  const AppMobileDrawerNavItem({
    super.key,
    required this.icon,
    required this.label,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: MySize.size12,
          vertical: MySize.size4,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: MySize.size16,
          vertical: MySize.size12,
        ),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(MySize.size8),
        ),
        child: Row(
          children: [
            PlatformIcon(
              iconName: icon,
              size: MySize.size20,
              color: isSelected ? AppColors.primaryColor : AppColors.blackColor,
            ),
            SizedBox(width: MySize.size12),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color:
                      isSelected
                          ? AppColors.primaryColor
                          : AppColors.blackColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AppHeader extends StatelessWidget {
  final String title;
  final String subtitle;
  final bool isMobile;
  final Widget? trailing;
  final TextStyle? titleStyle;
  final TextStyle? subtitleStyle;
  const AppHeader({
    super.key,
    required this.title,
    required this.subtitle,
    required this.isMobile,
    this.trailing,
    this.titleStyle,
    this.subtitleStyle,
  });

  @override
  Widget build(BuildContext context) {
    if (isMobile) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style:
                titleStyle ??
                TextStyle(
                  fontSize: MySize.size20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
          ),
          SizedBox(height: MySize.size4),
          Text(
            subtitle,
            style:
                subtitleStyle ??
                TextStyle(
                  fontSize: MySize.size12,
                  color: AppColors.textSecondary,
                ),
          ),
          if (trailing != null) ...[SizedBox(height: MySize.size12), trailing!],
        ],
      );
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style:
                    titleStyle ??
                    TextStyle(
                      fontSize: MySize.size24,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
              ),
              SizedBox(height: MySize.size4),
              Text(
                subtitle,
                style:
                    subtitleStyle ??
                    TextStyle(
                      fontSize: MySize.size14,
                      color: AppColors.textSecondary,
                    ),
              ),
            ],
          ),
        ),
        if (trailing != null) ...[SizedBox(width: MySize.size24), trailing!],
      ],
    );
  }
}

class AppHeaderAndActionRow extends StatelessWidget {
  final String title;
  final String subtitle;
  final bool isMobile;
  final Widget? searchBar;
  final Widget? actionButton;
  const AppHeaderAndActionRow({
    super.key,
    required this.title,
    required this.subtitle,
    required this.isMobile,
    this.searchBar,
    this.actionButton,
  });

  @override
  Widget build(BuildContext context) {
    if (isMobile) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: MySize.size20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: MySize.size4),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: MySize.size12,
              color: AppColors.textSecondary,
            ),
          ),
          if (searchBar != null) ...[
            SizedBox(height: MySize.size16),
            searchBar!,
          ],
          if (actionButton != null) ...[
            SizedBox(height: MySize.size12),
            actionButton!,
          ],
        ],
      );
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: MySize.size24,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              SizedBox(height: MySize.size4),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
        if (actionButton != null) ...[
          SizedBox(width: MySize.size24),
          actionButton!,
        ],
      ],
    );
  }
}
